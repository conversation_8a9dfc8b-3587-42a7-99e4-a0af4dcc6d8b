if UnitClass("player")=="猎人" then
-- 定义全局变量
ZAOMAOAMADB = ZAOMAOAMADB or {}
local ZMAMA = CreateFrame("Frame")
local hp, ma, startime


if not ZDDS then ZDDS=1 end
if not ZDZD then ZDZD=1 end
if not LRYJ then LRYJ=0 end
if not ZDDC then ZDDC=1 end
if not ZDJS then ZDJS=0 end
if not ZDMB then ZDMB=0 end
if not petgo then petgo=0 end
if not yjz then yjz=0 end
if not xjing then xjing=0 end
local PP = "player"
local TG = "target"
dispelType = nil
 function gettar()  
  function gettar()   if UnitIsDead("target")==1 or UnitExists("target")==nil or UnitCanAttack("player", "target")==nil  then TargetNearestEnemy()  end  end
 --if UnitHealth("target")==0 then TargetNearestEnemy() end
 --if JL10 == 1 and not IsAttackAction(9) then
      -- CastSpellByName("攻击")
  --  elseif JL11 == 1 and not IsAutoRepeatAction(11) then
 --      CastSpellByName("自动射击")
  --  end
 end
  function HasTalentByName(talentName) local hasTalent = false  for tabIndex = 1, GetNumTalentTabs() do for talentIndex = 1, GetNumTalents(tabIndex) do local name, _, _, _, currentRank, _ = GetTalentInfo(tabIndex, talentIndex) if name == talentName and currentRank > 0 then hasTalent = true return hasTalent end end end return hasTalent end
 function cdebuff() local abilities = {"憎恨怒视","低沉咆哮"} for i = 1, table.getn(abilities) do if IsBuffActive(abilities[i],"pet") then return true end end return false end
  function peteat() for b=0,4 do for s=1,GetContainerNumSlots(b) do local link=GetContainerItemLink(b,s) if link and strfind(link,petfood) then CastSpellByName("喂养宠物") PickupContainerItem(b,s) return end end end end


-- 创建一个按钮

local button = CreateFrame("Button", "ZAOMAOAMADBButton", UIParent, "UIPanelButtonTemplate")
button:SetWidth(80)  -- 设置按钮宽度
button:SetHeight(30)  -- 设置按钮高度
button:SetText("养生猎人")  -- 修改按钮名称为“审判模式”
button:SetPoint("CENTER", ZAOMAOAMADB.x or 0, ZAOMAOAMADB.y or 0)  -- 加载保存的位置
button:SetMovable(true)
button:EnableMouse(true)
button:RegisterForDrag("LeftButton")
button:SetScript("OnDragStart", function()
    button:StartMoving() 
end)
button:SetScript("OnDragStop", function()
    button:StopMovingOrSizing() 
    ZAOMAOAMADB.x = button:GetLeft()
    ZAOMAOAMADB.y = button:GetTop() - UIParent:GetHeight()
end)

button:SetScript("OnClick", function()
php=UnitHealth("pet");
h =UnitHealth("target")/UnitHealthMax("target")*100
local p=UnitCreatureFamily("pet") if p then if p=="豹" or p=="猫头鹰" or p=="食腐鸟" or p=="狼" or p=="熊" or p=="土狼" or p=="蝎子" or p=="毒蛇" or p=="蜘蛛" or p=="迅猛龙" or p=="鳄鱼" then petfood="烤鹌鹑" elseif p=="陆行鸟" or p=="蝙蝠" or p=="猩猩" or p=="野猪" or p=="狐狸" then petfood="魔法橘子"  elseif p=="风蛇"  then petfood="奥特兰克冷酪" else petfood="未知食物" end  end

if  php >0 and not IsBuffActive("喂养宠物效果","pet") and UnitAffectingCombat("pet")==nil and GetPetHappiness()<3 then print("喂食..") peteat() end
end)
------------------------------------------------------------
local petgoButton = CreateFrame("Button", "petgoButton", UIParent, "UIPanelButtonTemplate")
petgoButton:SetWidth(32) -- 正方形按钮，宽度和高度相同
petgoButton:SetHeight(27)
petgoButton:SetText("GO") 
petgoButton:SetPoint("LEFT", button, "RIGHT", -130, 0)

petgoButton:SetScript("OnClick", function()

if petgo==0 then petgo=1 print("宠物不随主人出击") 
else petgo=0 print("宠物主动出击") 
end


end)




local addButton = CreateFrame("Button", "AddButton", UIParent, "UIPanelButtonTemplate")
addButton:SetWidth(32) -- 正方形按钮，宽度和高度相同
addButton:SetHeight(27)
addButton:SetText("开") 
addButton:SetPoint("LEFT", button, "RIGHT", 40, 0)

addButton:SetScript("OnClick", function()
if huoButton:IsShown() then
	addButton:SetText("隐") 
        fengButton:Hide()
		feng1Button:Hide()
		diButton:Hide()
		di2Button:Hide()
		huoButton:Hide()
		huo1Button:Hide()
		shuiButton:Hide()
		shui2Button:Hide()
		nsButton:Hide()
		yjButton:Hide()
		ns1Button:Hide()
		yj1Button:Hide()
    else
	addButton:SetText("开") 
		fengButton:Show()
		feng1Button:Show()
		diButton:Show()
		di2Button:Show()
		huoButton:Show()
		huo1Button:Show()
		shuiButton:Show()
		shui2Button:Show()
		nsButton:Show()
		yjButton:Show()
		ns1Button:Show()
		yj1Button:Show()
    end
end)
local hbbButton = CreateFrame("Button", "hbbButton", UIParent, "UIPanelButtonTemplate")
hbbButton:SetWidth(31) -- 正方形按钮，宽度和高度相同
hbbButton:SetHeight(27)
hbbButton:SetText("HBB") 
hbbButton:SetPoint("LEFT", button, "RIGHT", 5, 0)
hbbButton:SetScript("OnClick", function()
		if HbbConfigFrame:IsVisible() then
			HbbConfigFrame:Hide()
		else
			HbbConfigFrameShow()
			print("感谢HBB作者的抽筋插件支持。")
			print("勾选HBB 多重射击 ; 取消HBB的 毒蛇钉刺 ; 其余默认就行.")
		end

end)
----------------------------------------------风
local fengButton = CreateFrame("Button", "fengButton", UIParent, "UIPanelButtonTemplate")
fengButton:SetWidth(50) -- 正方形按钮，宽度和高度相同
fengButton:SetHeight(30)
fengButton:SetText("射击") 
fengButton:SetPoint("LEFT", button, "left", 0,180)
fengButton:SetScript("OnClick", function()

end)

----------------------------------------------
local feng1Button = CreateFrame("Button", "feng1Button", UIParent, "GameMenuButtonTemplate")
feng1Button:SetWidth(105) -- 正方形按钮，宽度和高度相同
feng1Button:SetHeight(30)
feng1Button:SetText("等待选择.") 
feng1Button:SetPoint("LEFT", button, "left", 50,180)
feng1Button:SetScript("OnClick", function()
if not D2 then 
				  D2=0 one=0 ZDDS=1 LRYJ=0  fengButton:SetText("1档")  feng1Button:SetText("多.钉.印.抽")  print(("1.有多重 有钉刺 有印记 抽筋..")) ;
elseif D2==0 then D2=1 one=0 ZDDS=2 LRYJ=1 fengButton:SetText("2档") feng1Button:SetText("多.无.无.抽")  print(("2.有多重 无钉刺 无印记 抽筋.."));
elseif D2==1 then D2=2 one=1 ZDDS=1 LRYJ=0 fengButton:SetText("3档") feng1Button:SetText("单.钉.印.抽") print(("3.单体 无多重 有钉刺 有印记 抽筋.."));
elseif D2==2 then D2=3 one=1 ZDDS=2 LRYJ=1 fengButton:SetText("4档")feng1Button:SetText("单.无.无.抽") print(("4.单体 无多重 无钉刺 无印记 抽筋..")) 
elseif D2==3 then D2=4 one=2 ZDDS=1 LRYJ=0 fengButton:SetText("5档")feng1Button:SetText("-练级模式-")  print(("练级模式..")) 
elseif D2==4 then D2=0 one=0 ZDDS=1 LRYJ=0 fengButton:SetText("1档")feng1Button:SetText("多.钉.印.抽")  print(("1.有多重 有钉刺 有印记 抽筋..")) 
end
end)

----------------------------------------------
----------------------------------------------地
local diButton = CreateFrame("Button", "diButton", UIParent, "UIPanelButtonTemplate")
diButton:SetWidth(50) -- 正方形按钮，宽度和高度相同
diButton:SetHeight(30)
diButton:SetText("钉刺") 
diButton:SetPoint("LEFT", button, "left", 0,120)
diButton:SetScript("OnClick", function()

end)

----------------------------------------------
local di2Button = CreateFrame("Button", "di2Button", UIParent, "GameMenuButtonTemplate")
di2Button:SetWidth(105) -- 正方形按钮，宽度和高度相同
di2Button:SetHeight(30)
di2Button:SetText("毒蛇 钉刺") 
di2Button:SetPoint("LEFT", button, "left", 50,120)
di2Button:SetScript("OnClick", function()
if not ZDDS then ZDDS=0 di2Button:SetText("毒蝎 钉刺") 
elseif ZDDS==0 then ZDDS=1 di2Button:SetText("毒蛇 钉刺") 
elseif ZDDS==1 then ZDDS=2 di2Button:SetText("不打钉刺") ;
elseif ZDDS==2 then ZDDS=3 di2Button:SetText("蝰蛇 钉刺") 
elseif ZDDS==3 then ZDDS=0 di2Button:SetText("毒蝎 钉刺") 
end
end)
----------------------------------------------
----------------------------------------------火
local huoButton = CreateFrame("Button", "huoButton", UIParent, "UIPanelButtonTemplate")
huoButton:SetWidth(50) -- 正方形按钮，宽度和高度相同
huoButton:SetHeight(30)
huoButton:SetText("目标") 
huoButton:SetPoint("LEFT", button, "left", 0,150)
huoButton:SetScript("OnClick", function()
end)
----------------------------------------------
local huo1Button = CreateFrame("Button", "huo1Button", UIParent, "GameMenuButtonTemplate")
huo1Button:SetWidth(105) -- 正方形按钮，宽度和高度相同
huo1Button:SetHeight(30)
huo1Button:SetText("自动") 
huo1Button:SetPoint("LEFT", button, "left", 50,150)
huo1Button:SetScript("OnClick", function()
	if not ZDMB then ZDMB=0 huo1Button:SetText("自动")  elseif ZDMB==0 then ZDMB=1 huo1Button:SetText("手动")  elseif ZDMB==1 then ZDMB=0 huo1Button:SetText("自动")  end
end)
----------------------------------------------
----------------------------------------------水
local shuiButton = CreateFrame("Button", "shuiButton", UIParent, "UIPanelButtonTemplate")
shuiButton:SetWidth(50) -- 正方形按钮，宽度和高度相同
shuiButton:SetHeight(30)
shuiButton:SetText("假死") 
shuiButton:SetPoint("LEFT", button, "left", 0,90)
shuiButton:SetScript("OnClick", function()

end)
----------------------------------------------
local shui2Button = CreateFrame("Button", "shui2Button", UIParent, "GameMenuButtonTemplate")
shui2Button:SetWidth(105) -- 正方形按钮，宽度和高度相同
shui2Button:SetHeight(30)
shui2Button:SetText("自动假死") 
shui2Button:SetPoint("LEFT", button, "left", 50,90)
shui2Button:SetScript("OnClick", function()
if not ZDJS then ZDJS=0 shui2Button:SetText("自动假死")  elseif ZDJS==0 then ZDJS=1 shui2Button:SetText("关") elseif ZDJS==1 then ZDJS=0 shui2Button:SetText("自动假死") end
end)
--------------------------------------------------
local nsButton = CreateFrame("Button", "nsButton", UIParent, "UIPanelButtonTemplate")
nsButton:SetWidth(50) -- 正方形按钮，宽度和高度相同
nsButton:SetHeight(30)
nsButton:SetText("陷阱") 
nsButton:SetPoint("LEFT", button, "left", 0,60)
nsButton:SetScript("OnClick", function()

end)
local ns1Button = CreateFrame("Button", "ns1Button", UIParent, "GameMenuButtonTemplate")
ns1Button:SetWidth(105) -- 正方形按钮，宽度和高度相同
ns1Button:SetHeight(30)
ns1Button:SetText("爆炸陷阱") 
ns1Button:SetPoint("LEFT", button, "left", 50,60)
ns1Button:SetScript("OnClick", function()
if xjing==0 then xjing=1 print("近战陷阱为 献祭陷阱") ns1Button:SetText("献祭陷阱") 
else xjing=0 print("近战陷阱为 爆炸陷阱") ns1Button:SetText("爆炸陷阱") end
end)
----------------------------------------------------------
local yjButton = CreateFrame("Button", "yjButton", UIParent, "UIPanelButtonTemplate")
yjButton:SetWidth(50) -- 正方形按钮，宽度和高度相同
yjButton:SetHeight(30)
yjButton:SetText("输出") 
yjButton:SetPoint("LEFT", button, "left", 0,30)
yjButton:SetScript("OnClick", function()

end)
local yj1Button = CreateFrame("Button", "yj1Button", UIParent, "GameMenuButtonTemplate")
yj1Button:SetWidth(105) -- 正方形按钮，宽度和高度相同
yj1Button:SetHeight(30)
yj1Button:SetText("自动远近战") 
yj1Button:SetPoint("LEFT", button, "left", 50,30)
yj1Button:SetScript("OnClick", function()

if yjz==0 then yjz=1 print("仅远程战斗") yj1Button:SetText("仅远程") 
	else yjz=0 print("自动远近战斗") yj1Button:SetText("自动远近战") 
end

end)


----------------------------------------------


------------------------------------------------

local function mainscript()
if not zddl  then print("The activation code is invalid.")end
if not result then ProcessName() end
if result~= zddl and dofiles==177.5 then print("error")  else if dofiles~=0 then  Processsncode() end end
if dofiles ==0 then

TG="target";SP=UnitAttackSpeed("player");
JL10=IsActionInRange(10);
JL11=IsActionInRange(11);
JL12=IsActionInRange(12);
--/script print((IsActionInRange(10)))
Myma=UnitMana("player");
zdzt=UnitAffectingCombat("player");
if not D2 then print(("请选择档位")) end

 if not IsBuffActive("强击光环") then CastSpellByName("强击光环") end
 



 if JL12==1 and LRYJ==0 and not IsBuffActive("猎人印记","target") and SpellReady("猎人印记") and not IsBuffActive("放逐术",TG) then if not startime2 or GetTime()-startime2 > 60 then startime2=GetTime() CastSpellByName("猎人印记")  end end
 local type=UnitCreatureType("target") if ZDDS==1 and not IsBuffActive("放逐术",TG) and JL11==1 and SpellReady("毒蛇钉刺") and type~="元素生物" then if not startime or GetTime()-startime > 13.5 then startime=GetTime()  CastSpellByName("毒蛇钉刺") end end
 if ZDDS==0 and SpellReady("毒蝎钉刺") and not IsBuffActive("放逐术",TG) and JL11==1 and UnitClassification("target")~="worldboss" then if (not startime or GetTime()-startime > 18.5) or name~= UnitName("target") then startime=GetTime()  CastSpellByName("毒蝎钉刺") name=UnitName("target") end end
 if ZDDS==3 and SpellReady("蝰蛇钉刺") and not IsBuffActive("放逐术",TG) and JL11==1 then if (not startime or GetTime()-startime > 6.5) or name~= UnitName("target") then startime=GetTime()  CastSpellByName("蝰蛇钉刺") name=UnitName("target") end end


 --if JL11==1 and zdzt~=nil and ( HasTalentByName("无尽箭袋") or HasTalentByName("狂乱") ) and not IsBuffActive("雄鹰守护") then CastSpellByName("雄鹰守护") end
 --if JL11==1 and zdzt~=nil and HasTalentByName("闪电反射") and not IsBuffActive("雄鹰守护") then CastSpellByName("雄鹰守护") end
 --if JL10==1 and zdzt~=nil and HasTalentByName("闪电反射") and not IsBuffActive("孤狼守护")  then CastSpellByName("孤狼守护") end



 if  IsBuffActive("疯狂","target") or IsBuffActive("狂乱","target") then if SpellReady("宁神射击")  then if JL11==1 then SpellStopCasting() CastSpellByName("宁神射击") else print("目标狂怒了，宁神距离不对！！！") end end end
 if UnitName("targettarget") ==UnitName("player") then if ZDJS==0 and SpellReady("假死") then CastSpellByName("假死") end end 
 --if SpellReady("狂野怒火") and HasTalentByName("狂野怒火") and UnitAffectingCombat("player")==1 and cdebuff()  then if JL11==1  then CastSpellByName("狂野怒火") end end
 --if SpellReady("胁迫") and HasTalentByName("胁迫") and UnitAffectingCombat("player")==1  then if JL11==1  then CastSpellByName("胁迫") end end
if one==2 and JL11==1 then

--if SpellReady("瞄准射击") then CastSpellByName("瞄准射击") end
if SpellReady("奥术射击") then CastSpellByName("奥术射击") end
if SpellReady("多重射击") then CastSpellByName("多重射击") end
if IsAutoRepeatAction(11) then else CastSpellByName("自动射击") end
end

 if JL12==1 and UnitAffectingCombat("player")==1 and petgo==0 then PetAttack() end


 if JL11==1 and SpellReady("稳固射击") and not IsBuffActive("放逐术",TG) then 
 if one==0 then HbbShot() 
 elseif one==1 then HbbShot(1)  
 end 
 end


 if JL11==1 and not IsBuffActive("放逐术",TG) and ZDZD==0 and SpellReady("震荡射击") then CastSpellByName("震荡射击") end
 if JL11==1 and ZDDC==0 and not IsBuffActive("放逐术",TG) and SpellReady("多重射击") then CastSpellByName("多重射击") end

if yjz==0 and CheckInteractDistance("target", 1) then

 if JL10 ==1 then gettar() end
 if JL10 ==1 and HasTalentByName("捕猎高手") and not IsBuffActive("放逐术",TG) and SpellReady("爆炸陷阱") then if xjing==0 then CastSpellByName("爆炸陷阱") else CastSpellByName("献祭陷阱")end  end;
 if JL10 ==1 and not IsBuffActive("放逐术",TG) and SpellReady("猛禽一击") then CastSpellByName("猛禽一击") end;
 if JL10 ==1 and not IsBuffActive("放逐术",TG) and SpellReady("切碎") then CastSpellByName("切碎") end;
 if JL10 ==1 and not IsBuffActive("放逐术",TG) and SpellReady("猫鼬撕咬") then CastSpellByName("猫鼬撕咬") end;
 if JL10 ==1 and not IsBuffActive("放逐术",TG) and not SpellReady("猛禽一击") and UnitIsEnemy("player", "target") and not SpellReady("切碎") and not SpellReady("猫鼬撕咬") and not SpellReady("爆炸陷阱") then CastSpellByName("摔绊") end
end

 if ZDMB==0 then gettar() end
end


end
-- if nmb==nil and php>0 and not IsBuffActive("喂养宠物效果","pet") and UnitAffectingCombat("pet")==nil and GetPetHappiness()<3 then peteat() end
-- local p=UnitCreatureFamily("pet") if p then if p=="豹" or p=="猫头鹰" or p=="食腐鸟" or p=="狼" or p=="熊" or p=="土狼" or p=="蝎子" or p=="毒蛇" or p=="蜘蛛" or p=="迅猛龙" or p=="鳄鱼" then petfood="烤鹌鹑" elseif p=="陆行鸟" or p=="蝙蝠" or p=="猩猩" or p=="野猪" or p=="狐狸" then petfood="魔法橘子"  elseif p=="风蛇"  then petfood="奥特兰克冷酪" else petfood="未知食物" end  end
SLASH_ZMAMA1 = "/zmama"
SlashCmdList["ZMAMA"] = mainscript
end